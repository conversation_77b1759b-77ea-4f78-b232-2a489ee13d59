<template>
  <q-dialog v-model:model-value="isShown" class="dialog-positions">
    <q-card class="positions-main">
      <!-- Header -->
      <div class="positions-header">
        <div class="header-left">
          <q-avatar class="user-avatar">
            <img v-if="userProfile?.profileImage" :src="userProfile.profileImage" loading="lazy" decoding="async">
            <div v-else class="profile-none"></div>
          </q-avatar>
          <div class="text-h6">{{ userProfile?.name ? `${userProfile.name}'s Positions` : "User Positions" }}</div>
        </div>
        <q-btn icon="close" size="sm" flat @click="onClickClose" />
      </div>

      <!-- Loading state -->
      <div v-if="isLoading" class="loading-container">
        <q-spinner size="lg" />
        <div class="q-mt-sm">Loading positions...</div>
      </div>

      <!-- No positions state -->
      <div v-else-if="groupedPositions.length === 0" class="no-positions-container">
        No positions found
      </div>

      <!-- Positions content -->
      <div v-else class="positions-content">
        <!-- Column headers -->
        <div class="positions-header-row">
          <div class="header-icon-space"></div>
          <div class="header-data-section">
            <div class="header-market" @click="onClickSort('title')" :class="getSortClass('title')">
              Market
              <q-icon :name="getSortIcon('title')" size="xs" />
            </div>
            <div class="header-shares-group" @click="onClickSort('shares')" :class="getSortClass('shares')">
              Shares
              <q-icon :name="getSortIcon('shares')" size="xs" />
            </div>
            <div class="header-price-group" @click="onClickSort('avgprice')" :class="getSortClass('avgprice')">
              Price
              <q-icon :name="getSortIcon('avgprice')" size="xs" />
            </div>
            <div class="header-cost-group" @click="onClickSort('cost')" :class="getSortClass('cost')">
              Value
              <q-icon :name="getSortIcon('cost')" size="xs" />
            </div>
            <div class="header-pnl-group" @click="onClickSort('pnl')" :class="getSortClass('pnl')">
              P&L
              <q-icon :name="getSortIcon('pnl')" size="xs" />
            </div>
          </div>
        </div>

        <!-- Event groups -->
        <div v-for="eventGroup in groupedPositions" :key="eventGroup.eventSlug" class="event-group">
          <!-- Event container: flex row with icon and data -->
          <div class="event-container">
            <!-- Event icon (left side) -->
            <div class="event-icon">
              <img :src="eventGroup.icon" :alt="eventGroup.title" />
            </div>

            <!-- Data container: flex column with title and position rows -->
            <div class="data-container">
              <!-- Event title -->
              <router-link :to="`/events/${eventGroup.eventSlug}`" class="event-title-link">
                <div class="event-title">
                  {{ eventGroup.title }}
                </div>
              </router-link>

              <!-- Position data rows -->
              <div v-for="position in eventGroup.positions" :key="position.asset" class="position-data-row">
                <div class="row-market">{{ getPositionDisplayTitle(position, eventGroup.title) }}</div>
                <div class="row-shares" :class="getOutcomeClass(position.outcome)">{{ formatDecimal(position.size, 0, true) }}</div>
                <div class="row-outcome" :class="getOutcomeClass(position.outcome)">
                  {{ position.outcome }}
                </div>
                <div class="row-avgprice">{{ formatCents(position.avgPrice, 0) }}</div>
                <div class="row-arrow">→</div>
                <div class="row-curprice">{{ formatCents(position.curPrice, 0) }}</div>
                <div class="row-cost">{{ formatCurrency(position.initialValue, 0) }}</div>
                <div class="row-cost-arrow">→</div>
                <div class="row-value">{{ formatCurrency(position.currentValue, 0) }}</div>
                <div class="row-pnl-cash" :class="getPnlClass(position.cashPnl)">
                  {{ position.cashPnl >= 0 ? '+' : '' }}{{ formatCurrency(position.cashPnl, 0) }}
                </div>
                <div class="row-pnl-percent" :class="getPnlClass(position.cashPnl)">
                  &nbsp;({{ position.percentPnl >= 0 ? '+' : '' }}{{ formatDecimal(position.percentPnl, 1, true) }}%)
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { PolyApiPosition, PolyGammaMarket, PolyUserProfileResponse } from "@shared/api-dataclasses-shared";
import { formatCurrency, formatCents, formatDecimal } from "src/utils";
import { useApi } from "src/api";

const isShown = defineModel("isShown", { type: Boolean, default: false });

//Props
const props = defineProps<{
  userProxyWallet: string;
}>();

interface EventGroup {
  eventSlug: string;
  title: string;
  icon: string;
  positions: EnhancedPosition[];
}

interface EnhancedPosition extends PolyApiPosition {
  marketTitle?: string;
  eventTitle?: string;
  eventIcon?: string;
}

const api = useApi();
const isLoading = ref(false);
const positions = ref<PolyApiPosition[]>([]);
const markets = ref<PolyGammaMarket[]>([]);
const userProfile = ref<PolyUserProfileResponse | null>(null);
const sortBy = ref<string>("title");
const sortDesc = ref(false);
const dynamicWidths = ref({
  shares: 55,
  outcome: 60,
  cost: 60,
  value: 60,
  pnlCash: 50
});

const groupedPositions = computed(() => {
  if (!positions.value.length || !markets.value.length) return [];

  //Create enhanced positions with market data
  const enhancedPositions: EnhancedPosition[] = positions.value.map(position => {
    const market = markets.value.find(m => m.conditionId === position.conditionId);
    return {
      ...position,
      marketTitle: market?.groupItemTitle || position.title,
      eventTitle: market?.events?.[0]?.title || position.title,
      eventIcon: market?.events?.[0]?.icon || position.icon
    };
  });

  //Group positions by event
  const groups: Record<string, EventGroup> = {};

  for (const position of enhancedPositions) {
    if (!groups[position.eventSlug]) {
      groups[position.eventSlug] = {
        eventSlug: position.eventSlug,
        title: position.eventTitle || position.title,
        icon: position.eventIcon || position.icon,
        positions: []
      };
    }
    groups[position.eventSlug].positions.push(position);
  }

  //Convert to array and sort each group's positions
  const groupArray = Object.values(groups);

  for (const group of groupArray) {
    group.positions.sort((a, b) => {
      let aVal: any, bVal: any;

      switch (sortBy.value) {
        case "title":
          aVal = (a.marketTitle || a.title).toLowerCase();
          bVal = (b.marketTitle || b.title).toLowerCase();
          break;
        case "outcome":
          aVal = a.outcome.toLowerCase();
          bVal = b.outcome.toLowerCase();
          break;
        case "avgprice":
          aVal = a.avgPrice;
          bVal = b.avgPrice;
          break;
        case "price":
          aVal = a.curPrice;
          bVal = b.curPrice;
          break;
        case "shares":
          aVal = a.size;
          bVal = b.size;
          break;
        case "cost":
          aVal = a.initialValue;
          bVal = b.initialValue;
          break;
        case "value":
          aVal = a.currentValue;
          bVal = b.currentValue;
          break;
        case "pnl":
          aVal = a.cashPnl;
          bVal = b.cashPnl;
          break;
        default:
          return 0;
      }

      if (aVal < bVal) return sortDesc.value ? 1 : -1;
      if (aVal > bVal) return sortDesc.value ? -1 : 1;
      return 0;
    });
  }

  return groupArray;
});

async function fetchPositions() {
  if (!props.userProxyWallet) return;

  isLoading.value = true;
  try {
    //Start fetching user profile (don't await yet)
    const profilePromise = api.getUserProfile(props.userProxyWallet);

    //First fetch positions
    const positionsData = await api.getPositions(props.userProxyWallet);
    positions.value = positionsData;

    //Then fetch market data for the specific condition IDs from positions
    if (positionsData.length > 0) {
      const conditionIds = positionsData.map(p => p.conditionId);
      const marketsData = await api.getMarkets(undefined, conditionIds);
      markets.value = marketsData;
    }
    else {
      markets.value = [];
    }

    //Calculate dynamic widths after data is loaded
    calculateDynamicWidths();

    //Finally await the user profile
    userProfile.value = await profilePromise;
  }
  catch (error) {
    console.error("Failed to fetch positions:", error);
  }
  finally {
    isLoading.value = false;
  }
}

function onClickSort(column: string) {
  if (sortBy.value === column) {
    sortDesc.value = !sortDesc.value;
  }
  else {
    sortBy.value = column;
    //Default to ascending for market/title, descending for all others
    sortDesc.value = column === "title" ? false : true;
  }
}

function getSortClass(column: string) {
  return {
    "sort-active": sortBy.value === column,
    "cursor-pointer": true
  };
}

function getSortIcon(column: string) {
  if (sortBy.value !== column) return "unfold_more";
  return sortDesc.value ? "keyboard_arrow_down" : "keyboard_arrow_up";
}

function getOutcomeClass(outcome: string) {
  const lower = outcome.toLowerCase();
  if (lower.includes("yes") || lower.includes("true")) return "text-yes bg-yes";
  if (lower.includes("no") || lower.includes("false")) return "text-no bg-no";
  return "";
}

function getPnlClass(pnl: number) {
  if (pnl > 0) return "text-yes";
  if (pnl < 0) return "text-no";
  return "";
}

function getPositionDisplayTitle(position: EnhancedPosition, eventTitle: string) {
  const marketTitle = position.marketTitle || position.title;
  //If market title is the same as event title, don't show it (redundant)
  if (marketTitle === eventTitle) {
    return "";
  }
  return marketTitle;
}

function measureTextWidth(text: string): number {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return 0;

  //Match the font used in the cells
  context.font = '13px monospace';
  return context.measureText(text).width;
}

function calculateDynamicWidths() {
  if (!positions.value.length) return;

  const sharesWidths = positions.value.map(p => measureTextWidth(formatDecimal(p.size, 0, true)));
  const outcomeWidths = positions.value.map(p => measureTextWidth(p.outcome));
  const costWidths = positions.value.map(p => measureTextWidth(formatCurrency(p.initialValue, 0)));
  const valueWidths = positions.value.map(p => measureTextWidth(formatCurrency(p.currentValue, 0)));
  const pnlCashWidths = positions.value.map(p => {
    const formatted = `${p.cashPnl >= 0 ? '+' : ''}${formatCurrency(p.cashPnl, 0)}`;
    return measureTextWidth(formatted);
  });

  dynamicWidths.value = {
    shares: Math.max(...sharesWidths, 30) + 6, //Min 30px + 2px padding
    outcome: Math.max(...outcomeWidths, 30) + 6, //Min 30px + 2px padding
    cost: Math.max(...costWidths, 40) + 2, //Min 40px + 2px padding
    value: Math.max(...valueWidths, 40) + 2, //Min 40px + 2px padding
    pnlCash: Math.max(...pnlCashWidths, 40) + 2 //Min 40px + 2px padding
  };

  //Apply to CSS custom properties
  document.documentElement.style.setProperty('--shares-width', `${dynamicWidths.value.shares}px`);
  document.documentElement.style.setProperty('--outcome-width', `${dynamicWidths.value.outcome}px`);
  document.documentElement.style.setProperty('--cost-width', `${dynamicWidths.value.cost}px`);
  document.documentElement.style.setProperty('--value-width', `${dynamicWidths.value.value}px`);
  document.documentElement.style.setProperty('--pnl-cash-width', `${dynamicWidths.value.pnlCash}px`);

  //Update shares group header width (shares + outcome)
  const sharesGroupWidth = dynamicWidths.value.shares + dynamicWidths.value.outcome;
  document.documentElement.style.setProperty('--shares-group-width', `${sharesGroupWidth}px`);

  //Update cost group header width (cost + arrow + value)
  const costGroupWidth = dynamicWidths.value.cost + 16 + dynamicWidths.value.value;
  document.documentElement.style.setProperty('--cost-group-width', `${costGroupWidth}px`);

  //Update pnl group header width (cash + percent)
  const pnlGroupWidth = dynamicWidths.value.pnlCash + 50; //50px for percentage
  document.documentElement.style.setProperty('--pnl-group-width', `${pnlGroupWidth}px`);
}

function onClickClose() {
  isShown.value = false;
}

//Watch for dialog opening to fetch positions
watch(() => isShown.value, (newVal) => {
  if (newVal) {
    fetchPositions();
  }
});

//Watch for positions changes to recalculate widths
watch(positions, () => {
  calculateDynamicWidths();
}, { deep: true });
</script>

<style scoped lang="scss">
.dialog-positions {
  .positions-main {
    width: auto;
    height: auto;
    max-width: none !important;
    max-height: 90vh;
    min-height: 60vh;
    min-width: 900px;
    padding: 0;
    display: flex;
    flex-direction: column;
  }
}

.positions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.user-avatar img {
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.profile-none {
  background-color: #333333;
  border-radius: 50%;
  width: 100%;
  height: 100%;
}

.positions-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
}

.no-positions-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
  color: #999;
  font-size: 16px;
}

.positions-header-row {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.header-icon-space {
  width: 62px;
  flex-shrink: 0;
}

.header-data-section {
  flex: 1;
  display: flex;
  align-items: center;
}

.header-market {
  width: 150px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

.header-shares-group {
  width: var(--shares-group-width, 115px);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-price-group {
  width: 71px; /* 30px + 16px + 25px = avgprice + arrow + curprice */
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-cost-group {
  width: var(--cost-group-width, 136px);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-pnl-group {
  width: var(--pnl-group-width, 100px);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.sort-active {
  color: #1976d2;
}

.event-group {
  border-bottom: 1px solid #e0e0e0;
}

.event-container {
  display: flex;
  flex-direction: row;
  padding: 0 16px;
  background-color: #fafafa;
}

.event-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  padding-top: 4px;

  img {
    width: 42px;
    height: 42px;
    border-radius: 4px;
    object-fit: cover;
  }
}

.data-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 12px;
}

.event-title-link {
  text-decoration: none;
  color: inherit;

  &:hover {
    text-decoration: none;
  }
}

.event-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  padding: 4px 0 4px 0;

  &:hover {
    color: #1976d2;
    cursor: pointer;
  }
}

.position-data-row {
  display: flex;
  align-items: center;
  height: 24px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;

  &:hover {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }
}

.row-market {
  width: 150px;
  flex-shrink: 0;
  color: #333;
  padding-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.row-outcome {
  width: var(--outcome-width, 60px);
  height: 100%;
  flex-shrink: 0;
  font-weight: 600;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.row-avgprice {
  width: 30px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
  color: #717171;
}

.row-arrow {
  width: 16px;
  flex-shrink: 0;
  text-align: center;
  color: #999;
  font-size: 12px;
  font-weight: bold;
}

.row-curprice {
  width: 32px;
  flex-shrink: 0;
  text-align: left;
  font-family: monospace;
}

.row-shares {
  width: var(--shares-width, 55px);
  height: 100%;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.row-cost {
  width: var(--cost-width, 60px);
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
  color: #717171;
}

.row-cost-arrow {
  width: 16px;
  flex-shrink: 0;
  text-align: center;
  color: #999;
  font-size: 12px;
  font-weight: bold;
}

.row-value {
  width: var(--value-width, 60px);
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
}

.row-pnl-cash {
  width: var(--pnl-cash-width, 50px);
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
  font-weight: 600;
}

.row-pnl-percent {
  width: 50px;
  flex-shrink: 0;
  text-align: left;
  font-family: monospace;
  font-weight: 600;
  font-size: 11px;
}
</style>
